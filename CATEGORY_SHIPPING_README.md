# نظام فئات المنتجات ورسوم التوصيل الإضافية

## نظرة عامة

تم إضافة نظام جديد لحساب رسوم التوصيل الإضافية بناءً على فئات المنتجات في السلة. هذا النظام يسمح بإضافة رسوم إضافية لفئات معينة من المنتجات (مثل المنتجات الثقيلة) إلى تكلفة التوصيل الأساسية.

## كيفية عمل النظام

### المثال المطلوب:
- سعر التوصيل الأساسي للولاية: 500 د.ج
- رسوم فئة المنتجات الثقيلة: 200 د.ج
- **النتيجة النهائية**: 700 د.ج (500 + 200)

## الميزات الجديدة

### 1. جدول قاعدة البيانات الجديد
- تم إنشاء جدول `wp_form_elrakami_product_categories_shipping`
- يحفظ معرف الفئة، اسمها، ونوعها، والرسوم الإضافية

### 2. واجهة إدارية جديدة
- صفحة إدارة في: **Form Elrakami > فئات المنتجات**
- إمكانية إضافة/حذف فئات ورسومها الإضافية
- عرض قائمة بجميع الفئات المُعرَّفة حالياً

### 3. حساب تلقائي للرسوم
- يتم حساب الرسوم الإضافية تلقائياً عند إضافة منتجات للسلة
- تجنب تكرار الرسوم لنفس الفئة (حتى لو كان هناك عدة منتجات من نفس الفئة)
- عرض تفصيلي للرسوم في الطلب النهائي

## طريقة الاستخدام

### 1. إعداد الفئات ورسومها

1. اذهب إلى **لوحة التحكم > Form Elrakami > فئات المنتجات**
2. اختر الفئة من القائمة المنسدلة
3. أدخل الرسوم الإضافية بالدينار الجزائري
4. اضغط "إضافة الفئة"

### 2. ربط المنتجات بالفئات

النظام يستخدم فئات WooCommerce الموجودة بالفعل:
1. اذهب إلى **المنتجات > تحرير منتج**
2. في قسم "فئات المنتج"، اختر الفئة المناسبة
3. احفظ المنتج

### 3. مراقبة النتائج

عند إضافة منتجات للسلة:
- سيتم حساب التكلفة الأساسية للتوصيل
- سيتم إضافة الرسوم الإضافية للفئات الموجودة في السلة
- سيظهر التفصيل في الطلب النهائي

## أمثلة عملية

### مثال 1: منتج واحد من فئة ثقيلة
- منتج: ثلاجة (فئة: أجهزة ثقيلة)
- تكلفة التوصيل الأساسية: 500 د.ج
- رسوم الفئة الثقيلة: 200 د.ج
- **المجموع**: 700 د.ج

### مثال 2: منتجات متعددة من فئات مختلفة
- منتج 1: ثلاجة (فئة: أجهزة ثقيلة - 200 د.ج)
- منتج 2: مواد كيميائية (فئة: مواد خطرة - 150 د.ج)
- تكلفة التوصيل الأساسية: 500 د.ج
- **المجموع**: 850 د.ج (500 + 200 + 150)

### مثال 3: منتجات متعددة من نفس الفئة
- منتج 1: ثلاجة (فئة: أجهزة ثقيلة)
- منتج 2: غسالة (فئة: أجهزة ثقيلة)
- تكلفة التوصيل الأساسية: 500 د.ج
- رسوم الفئة الثقيلة: 200 د.ج (مرة واحدة فقط)
- **المجموع**: 700 د.ج

## اختبار النظام

### صفحة الاختبار التلقائي
1. اذهب إلى **Form Elrakami > اختبار النظام**
2. اضغط "تشغيل الاختبارات"
3. تأكد من نجاح جميع الاختبارات

### الاختبارات المتضمنة:
- ✅ اختبار بنية قاعدة البيانات
- ✅ اختبار مدير الفئات
- ✅ اختبار حساب تكلفة التوصيل
- ✅ اختبار تكامل السلة

## الملفات المُضافة/المُعدَّلة

### ملفات جديدة:
- `includes/class-product-categories-shipping-manager.php` - مدير الفئات والرسوم
- `includes/class-categories-shipping-test.php` - نظام الاختبار
- `CATEGORY_SHIPPING_README.md` - هذا الملف

### ملفات مُعدَّلة:
- `includes/class-form-elrakami-activator.php` - إضافة إنشاء الجدول
- `includes/class-custom-cart-system.php` - تحديث حساب التكلفة
- `form-elrakami.php` - إضافة تضمين الملفات الجديدة

## الدوال الرئيسية

### في مدير الفئات:
- `get_category_additional_cost($category_id)` - الحصول على رسوم فئة
- `calculate_cart_additional_shipping_costs($cart_items)` - حساب إجمالي الرسوم

### في نظام السلة:
- `calculate_categories_additional_shipping_cost($cart)` - حساب رسوم السلة
- `get_cart_shipping_total()` - إجمالي تكلفة التوصيل (محدثة)

## ملاحظات مهمة

1. **عدم التكرار**: الرسوم الإضافية لا تتكرر لنفس الفئة حتى لو كان هناك عدة منتجات
2. **التوافق**: النظام متوافق مع فئات WooCommerce الموجودة
3. **المرونة**: يمكن إضافة/حذف/تعديل الفئات في أي وقت
4. **الشفافية**: يظهر تفصيل الرسوم في الطلب النهائي

## الدعم والصيانة

- جميع الاختبارات متاحة في صفحة "اختبار النظام"
- يمكن مراقبة الأخطاء في سجلات WordPress
- النظام يدعم التحديثات المستقبلية للإضافة

---

**تم تطوير هذا النظام بناءً على المتطلبات المحددة لحساب رسوم التوصيل الإضافية للفئات المختلفة من المنتجات.**
