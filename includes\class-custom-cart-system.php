<?php
/**
 * نظام السلة المخصص
 *
 * @since      1.0.0
 * @package    Form_Elrakami
 */

// منع الوصول المباشر
if (!defined('WPINC')) {
    die;
}

/**
 * صنف نظام السلة المخصص
 */
class Custom_Cart_System {

    /**
     * تهيئة الصنف
     */
    public function __construct() {
        // التحقق من تفعيل نظام السلة
        if (!$this->is_cart_system_enabled()) {
            return;
        }

        // إضافة hooks للواجهة الأمامية
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('wp_footer', array($this, 'add_cart_sidebar_html'));

        // إضافة أيقونة السلة العائمة (إذا كانت مفعلة)
        if ($this->is_cart_icon_enabled()) {
            add_action('wp_footer', array($this, 'add_floating_cart_icon'));
        }

        // إضافة AJAX handlers
        add_action('wp_ajax_add_to_custom_cart', array($this, 'handle_add_to_cart'));
        add_action('wp_ajax_nopriv_add_to_custom_cart', array($this, 'handle_add_to_cart'));
        add_action('wp_ajax_remove_from_custom_cart', array($this, 'handle_remove_from_cart'));
        add_action('wp_ajax_nopriv_remove_from_custom_cart', array($this, 'handle_remove_from_cart'));
        add_action('wp_ajax_update_cart_quantity', array($this, 'handle_update_quantity'));
        add_action('wp_ajax_nopriv_update_cart_quantity', array($this, 'handle_update_quantity'));
        add_action('wp_ajax_get_cart_contents', array($this, 'handle_get_cart_contents'));
        add_action('wp_ajax_nopriv_get_cart_contents', array($this, 'handle_get_cart_contents'));
        add_action('wp_ajax_complete_cart_order', array($this, 'handle_complete_order'));
        add_action('wp_ajax_nopriv_complete_cart_order', array($this, 'handle_complete_order'));


        // بدء الجلسة إذا لم تكن مبدوءة
        add_action('init', array($this, 'start_session'));
    }

    /**
     * التحقق من تفعيل نظام السلة
     */
    private function is_cart_system_enabled() {
        return get_option('form_elrakami_cart_system_enabled', 1) == 1;
    }

    /**
     * التحقق من تفعيل أيقونة السلة العائمة
     */
    private function is_cart_icon_enabled() {
        return get_option('form_elrakami_cart_icon_enabled', 1) == 1;
    }

    /**
     * التحقق من تفعيل فتح السلة تلقائياً
     */
    private function is_cart_auto_open_enabled() {
        return get_option('form_elrakami_cart_auto_open', 1) == 1;
    }

    /**
     * التحقق من تفعيل حفظ بيانات السلة
     */
    private function is_cart_save_data_enabled() {
        return get_option('form_elrakami_cart_save_data', 1) == 1;
    }

    /**
     * التحقق من تفعيل مسح السلة بعد الطلب
     */
    private function is_cart_clear_after_order_enabled() {
        return get_option('form_elrakami_cart_clear_after_order', 1) == 1;
    }

    /**
     * بدء الجلسة
     */
    public function start_session() {
        if (!session_id() && !headers_sent()) {
            session_start();
        }
    }

    /**
     * تحميل أصول الواجهة الأمامية
     */
    public function enqueue_frontend_assets() {
        // تحميل Font Awesome للأيقونات
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
            array(),
            '6.0.0'
        );

        // تحميل CSS
        wp_enqueue_style(
            'custom-cart-style',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/custom-cart.css',
            array('font-awesome'),
            FORM_ELRAKAMI_VERSION
        );

        // تحميل JavaScript
        wp_enqueue_script(
            'custom-cart-script',
            plugin_dir_url(dirname(__FILE__)) . 'public/js/custom-cart.js',
            array('jquery'),
            FORM_ELRAKAMI_VERSION,
            true
        );

        // إضافة متغيرات JavaScript
        wp_localize_script('custom-cart-script', 'customCart', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('custom_cart_nonce'),
            'currency' => 'دج',
            'settings' => array(
                'auto_open' => $this->is_cart_auto_open_enabled(),
                'save_data' => $this->is_cart_save_data_enabled(),
                'clear_after_order' => $this->is_cart_clear_after_order_enabled(),
                'default_button_text' => get_option('form_elrakami_cart_button_default_text', 'أضف إلى السلة'),
                'default_button_color' => get_option('form_elrakami_cart_button_default_color', '#28a745')
            ),
            'texts' => array(
                'added_to_cart' => 'تم إضافة المنتج إلى السلة',
                'removed_from_cart' => 'تم حذف المنتج من السلة',
                'cart_updated' => 'تم تحديث السلة',
                'error_occurred' => 'حدث خطأ، يرجى المحاولة مرة أخرى',
                'empty_cart' => 'السلة فارغة',
                'total' => 'المجموع',
                'shipping' => 'التوصيل',
                'final_total' => 'المجموع النهائي',
                'checkout' => 'إتمام الطلب',
                'continue_shopping' => 'متابعة التسوق'
            )
        ));
    }

    /**
     * إضافة أيقونة السلة العائمة
     */
    public function add_floating_cart_icon() {
        // الحصول على إعدادات الأيقونة العائمة
        $position = get_option('form_elrakami_floating_cart_position', 'top-left');
        $color = get_option('form_elrakami_floating_cart_color', '#28a745');
        $size = get_option('form_elrakami_floating_cart_size', 'medium');
        $shape = get_option('form_elrakami_floating_cart_shape', 'circle');

        ?>
        <div id="floating-cart-icon" class="floating-cart-icon <?php echo esc_attr($position); ?>">
            <div class="cart-icon-wrapper <?php echo esc_attr($size); ?> <?php echo esc_attr($shape); ?>"
                 style="background-color: <?php echo esc_attr($color); ?>; box-shadow: 0 4px 12px <?php echo esc_attr($color); ?>30;">
                <svg class="cart-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="cart-count">0</span>
            </div>
        </div>
        <?php
    }

    /**
     * إضافة HTML السلة الجانبية
     */
    public function add_cart_sidebar_html() {
        ?>
        <div id="cart-sidebar" class="cart-sidebar">
            <div class="cart-sidebar-overlay"></div>
            <div class="cart-sidebar-content">
                <div class="cart-sidebar-header">
                    <h3>سلة التسوق</h3>
                    <button class="cart-close-btn" type="button">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
                
                <div class="cart-sidebar-body">
                    <div class="cart-items-container">
                        <!-- سيتم ملء المحتوى بواسطة JavaScript -->
                    </div>
                </div>
                
                <div class="cart-sidebar-footer">
                    <div class="cart-totals">
                        <div class="cart-subtotal">
                            <span>المجموع الفرعي:</span>
                            <span class="subtotal-amount">0 دج</span>
                        </div>
                        <div class="cart-shipping">
                            <span>التوصيل:</span>
                            <span class="shipping-amount">0 دج</span>
                        </div>
                        <div class="cart-total">
                            <span>المجموع النهائي:</span>
                            <span class="total-amount">0 دج</span>
                        </div>
                    </div>
                    
                    <div class="cart-actions">
                        <button class="btn-checkout" type="button">إتمام الطلب</button>
                        <button class="btn-continue-shopping" type="button">متابعة التسوق</button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * معالجة إضافة منتج إلى السلة
     */
    public function handle_add_to_cart() {
        try {
            // التحقق من الأمان
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
                wp_send_json_error('خطأ في التحقق من الأمان');
                return;
            }

            $product_id = intval($_POST['product_id']);
            $variation_id = intval($_POST['variation_id'] ?? 0);
            $quantity = intval($_POST['quantity']) ?: 1;

            // جمع بيانات العميل من النموذج
            $customer_data = array();

            // جمع جميع البيانات المرسلة من النموذج
            foreach ($_POST as $key => $value) {
                if (!in_array($key, array('action', 'nonce', 'product_id', 'variation_id', 'quantity'))) {
                    $customer_data[$key] = sanitize_text_field($value);
                }
            }

            // التحقق من صحة البيانات
            if ($product_id <= 0) {
                wp_send_json_error('معرف المنتج غير صالح');
                return;
            }

            // تحديد المنتج المراد إضافته (المنتج الأساسي أو المتغير)
            $product_to_add = null;
            $actual_product_id = $product_id;
            $price = 0;

            if ($variation_id > 0) {
                // إذا كان هناك متغير، استخدم المتغير
                $variation_product = wc_get_product($variation_id);
                if ($variation_product && $variation_product->is_type('variation')) {
                    $product_to_add = $variation_product;
                    $actual_product_id = $variation_id;
                    $price = floatval($variation_product->get_price());
                    error_log("Custom Cart: استخدام متغير المنتج - ID: $variation_id, السعر: $price");
                } else {
                    wp_send_json_error('المتغير المحدد غير صالح');
                    return;
                }
            } else {
                // استخدام المنتج الأساسي
                $product_to_add = wc_get_product($product_id);
                if (!$product_to_add) {
                    wp_send_json_error('المنتج غير موجود');
                    return;
                }
                $price = floatval($product_to_add->get_price());
                error_log("Custom Cart: استخدام المنتج الأساسي - ID: $product_id, السعر: $price");
            }

            // حساب تكلفة التوصيل من طريقة التوصيل المختارة
            $shipping_cost = 0;
            if (isset($customer_data['shipping_method_option'])) {
                $shipping_cost = $this->get_shipping_cost_from_form($customer_data['shipping_method_option'], $customer_data);
                error_log("Custom Cart: طريقة التوصيل المختارة: " . $customer_data['shipping_method_option']);
                error_log("Custom Cart: تكلفة التوصيل المحسوبة: " . $shipping_cost);
            } else {
                error_log("Custom Cart: لم يتم العثور على طريقة التوصيل في البيانات");
                error_log("Custom Cart: البيانات المرسلة: " . print_r($customer_data, true));
            }

            // إضافة المنتج إلى السلة
            $cart_item = array(
                'product_id' => $product_id,
                'variation_id' => $variation_id,
                'actual_product_id' => $actual_product_id, // المعرف الفعلي (المنتج أو المتغير)
                'quantity' => $quantity,
                'price' => $price,
                'name' => $product_to_add->get_name(),
                'image' => wp_get_attachment_image_url($product_to_add->get_image_id(), 'thumbnail'),
                'customer_data' => $customer_data,
                'shipping_cost' => $shipping_cost,
                'added_time' => current_time('timestamp')
            );

            // الحصول على السلة الحالية
            $cart = $this->get_cart();

            // البحث عن المنتج في السلة (مع مراعاة المتغيرات)
            $found = false;
            foreach ($cart as $key => $item) {
                // مقارنة المعرف الفعلي للمنتج (يشمل المتغيرات)
                if ($item['actual_product_id'] == $actual_product_id) {
                    $cart[$key]['quantity'] += $quantity;
                    // تحديث بيانات العميل وتكلفة التوصيل
                    $cart[$key]['customer_data'] = $customer_data;
                    $cart[$key]['shipping_cost'] = $shipping_cost;
                    $found = true;
                    break;
                }
            }

            // إذا لم يوجد المنتج، أضفه
            if (!$found) {
                $cart[] = $cart_item;
            }

            // حفظ السلة
            $this->save_cart($cart);

            wp_send_json_success(array(
                'message' => 'تم إضافة المنتج إلى السلة',
                'cart_count' => $this->get_cart_count(),
                'cart_total' => $this->get_cart_total(),
                'shipping_total' => $this->get_cart_shipping_total()
            ));

        } catch (Exception $e) {
            error_log('خطأ في إضافة المنتج إلى السلة: ' . $e->getMessage());
            wp_send_json_error('حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * معالجة حذف منتج من السلة
     */
    public function handle_remove_from_cart() {
        try {
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
                wp_send_json_error('خطأ في التحقق من الأمان');
                return;
            }

        $product_id = intval($_POST['product_id']);
        $cart = $this->get_cart();

        // حذف المنتج من السلة (مع مراعاة المتغيرات)
        foreach ($cart as $key => $item) {
            // استخدام actual_product_id للمقارنة لدعم المتغيرات
            $item_actual_id = isset($item['actual_product_id']) ? $item['actual_product_id'] : $item['product_id'];
            if ($item_actual_id == $product_id) {
                unset($cart[$key]);
                break;
            }
        }

        // إعادة ترقيم المصفوفة
        $cart = array_values($cart);
        $this->save_cart($cart);

        wp_send_json_success(array(
            'message' => 'تم حذف المنتج من السلة',
            'cart_count' => $this->get_cart_count(),
            'cart_total' => $this->get_cart_total()
        ));

        } catch (Exception $e) {
            error_log('خطأ في حذف المنتج من السلة: ' . $e->getMessage());
            wp_send_json_error('حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى');
        }
    }

    /**
     * الحصول على السلة
     */
    private function get_cart() {
        $cart = isset($_SESSION['custom_cart']) ? $_SESSION['custom_cart'] : array();

        // تحديث السلة القديمة لدعم المتغيرات
        foreach ($cart as $key => $item) {
            if (!isset($item['actual_product_id'])) {
                $cart[$key]['actual_product_id'] = $item['product_id'];
                $cart[$key]['variation_id'] = 0;
            }
        }

        return $cart;
    }

    /**
     * حفظ السلة
     */
    private function save_cart($cart) {
        $_SESSION['custom_cart'] = $cart;
    }

    /**
     * الحصول على عدد المنتجات في السلة
     */
    private function get_cart_count() {
        $cart = $this->get_cart();
        $count = 0;
        foreach ($cart as $item) {
            $count += $item['quantity'];
        }
        return $count;
    }

    /**
     * الحصول على إجمالي السلة
     */
    private function get_cart_total() {
        $cart = $this->get_cart();
        $total = 0;
        foreach ($cart as $item) {
            $total += $item['price'] * $item['quantity'];
        }
        return $total;
    }

    /**
     * الحصول على إجمالي تكلفة التوصيل للسلة
     * يتم حساب التكلفة بناءً على أول منتج تمت إضافته إلى السلة + الرسوم الإضافية للفئات
     */
    private function get_cart_shipping_total() {
        $cart = $this->get_cart();

        if (empty($cart)) {
            return 0;
        }

        // الحصول على تكلفة التوصيل الأساسية من أول منتج في السلة
        $first_item = reset($cart);
        $base_shipping_cost = floatval($first_item['shipping_cost'] ?? 0);

        // حساب الرسوم الإضافية بناءً على فئات المنتجات
        $additional_shipping_cost = $this->calculate_categories_additional_shipping_cost($cart);

        return $base_shipping_cost + $additional_shipping_cost;
    }

    /**
     * حساب الرسوم الإضافية للتوصيل بناءً على فئات المنتجات في السلة
     */
    private function calculate_categories_additional_shipping_cost($cart) {
        // التحقق من وجود مدير فئات المنتجات
        if (!class_exists('Product_Categories_Shipping_Manager')) {
            return 0;
        }

        $categories_manager = new Product_Categories_Shipping_Manager();
        $total_additional_cost = 0;
        $processed_categories = array(); // لتجنب تكرار الرسوم لنفس الفئة

        foreach ($cart as $item) {
            $product_id = $item['product_id'];
            $variation_id = isset($item['variation_id']) ? $item['variation_id'] : 0;

            // استخدام معرف المتغير إذا كان موجوداً، وإلا استخدام معرف المنتج الأساسي
            $product_to_check = $variation_id > 0 ? $variation_id : $product_id;

            // الحصول على فئات المنتج
            $product_categories = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'ids'));

            if (!empty($product_categories)) {
                foreach ($product_categories as $category_id) {
                    // تجنب تكرار الرسوم لنفس الفئة
                    if (!in_array($category_id, $processed_categories)) {
                        $additional_cost = $categories_manager->get_category_additional_cost($category_id);
                        if ($additional_cost > 0) {
                            $total_additional_cost += $additional_cost;
                            $processed_categories[] = $category_id;
                        }
                    }
                }
            }
        }

        return $total_additional_cost;
    }

    /**
     * الحصول على تكلفة التوصيل من طريقة التوصيل المختارة في النموذج
     */
    private function get_shipping_cost_from_form($shipping_method_option, $customer_data = array()) {
        // أولاً، نحاول البحث عن التكلفة في البيانات المرسلة
        // البحث عن حقل يحتوي على التكلفة
        foreach ($customer_data as $key => $value) {
            // البحث عن حقول تحتوي على "cost" أو "price" أو "shipping"
            if (strpos(strtolower($key), 'cost') !== false ||
                strpos(strtolower($key), 'price') !== false ||
                strpos(strtolower($key), 'shipping') !== false) {
                if (is_numeric($value)) {
                    return floatval($value);
                }
            }
        }

        // إذا لم نجد التكلفة في البيانات، نحاول الحصول عليها من إعدادات النموذج
        $shipping_cost = $this->get_shipping_cost_by_method($shipping_method_option);

        if ($shipping_cost > 0) {
            return $shipping_cost;
        }

        // تحليل قيمة طريقة التوصيل للحصول على التكلفة
        // القيمة قد تأتي بصيغة: "company_id:cost" أو مجرد cost
        if (strpos($shipping_method_option, ':') !== false) {
            $parts = explode(':', $shipping_method_option);
            return floatval(end($parts));
        }

        // إذا كانت القيمة رقم مباشر
        if (is_numeric($shipping_method_option)) {
            return floatval($shipping_method_option);
        }

        return 0;
    }

    /**
     * الحصول على تكلفة التوصيل حسب طريقة التوصيل من إعدادات النموذج
     */
    private function get_shipping_cost_by_method($shipping_method) {
        // الحصول على إعدادات النموذج
        $form_settings = get_option('form_elrakami_settings', array());

        switch ($shipping_method) {
            case 'standard_shipping':
                return floatval($form_settings['default_shipping_1_cost'] ?? 0);

            case 'economy_shipping':
                return floatval($form_settings['default_shipping_2_cost'] ?? 0);

            case 'free_shipping':
                return 0;

            default:
                // محاولة البحث في بيانات شركات الشحن
                $shipping_companies_data = get_option('form_elrakami_shipping_companies_data', array());

                if (isset($shipping_companies_data[$shipping_method])) {
                    $company_data = $shipping_companies_data[$shipping_method];

                    // إرجاع أول تكلفة متاحة
                    if (isset($company_data['pricing']) && is_array($company_data['pricing'])) {
                        foreach ($company_data['pricing'] as $pricing_item) {
                            if (isset($pricing_item['price']) && $pricing_item['price'] > 0) {
                                return floatval($pricing_item['price']);
                            }
                        }
                    }
                }

                return 0;
        }
    }

    /**
     * معالجة تحديث كمية المنتج
     */
    public function handle_update_quantity() {
        if (!wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
            wp_send_json_error('خطأ في التحقق من الأمان');
            return;
        }

        $product_id = intval($_POST['product_id']);
        $quantity = intval($_POST['quantity']);

        if ($quantity <= 0) {
            wp_send_json_error('الكمية يجب أن تكون أكبر من صفر');
            return;
        }

        $cart = $this->get_cart();

        // تحديث كمية المنتج (مع مراعاة المتغيرات)
        foreach ($cart as $key => $item) {
            // استخدام actual_product_id للمقارنة لدعم المتغيرات
            $item_actual_id = isset($item['actual_product_id']) ? $item['actual_product_id'] : $item['product_id'];
            if ($item_actual_id == $product_id) {
                $cart[$key]['quantity'] = $quantity;
                break;
            }
        }

        $this->save_cart($cart);

        wp_send_json_success(array(
            'message' => 'تم تحديث الكمية',
            'cart_count' => $this->get_cart_count(),
            'cart_total' => $this->get_cart_total()
        ));
    }

    /**
     * معالجة الحصول على محتويات السلة
     */
    public function handle_get_cart_contents() {
        if (!wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
            wp_send_json_error('خطأ في التحقق من الأمان');
            return;
        }

        $cart = $this->get_cart();
        $cart_html = $this->generate_cart_html($cart);
        $shipping_total = $this->get_cart_shipping_total();
        $subtotal = $this->get_cart_total();
        $total = $subtotal + $shipping_total;

        wp_send_json_success(array(
            'cart_html' => $cart_html,
            'cart_count' => $this->get_cart_count(),
            'subtotal' => $subtotal,
            'shipping' => $shipping_total,
            'total' => $total,
            'empty' => empty($cart)
        ));
    }

    /**
     * توليد HTML للسلة
     */
    private function generate_cart_html($cart) {
        if (empty($cart)) {
            return '<div class="empty-cart">
                        <div class="empty-cart-icon">🛒</div>
                        <p>السلة فارغة</p>
                        <small>أضف منتجات لتبدأ التسوق</small>
                    </div>';
        }

        $html = '<div class="cart-items">';

        // الحصول على تكلفة التوصيل من أول منتج فقط
        $first_item = reset($cart);
        $main_shipping_cost = floatval($first_item['shipping_cost'] ?? 0);

        foreach ($cart as $index => $item) {
            $product_total = $item['price'] * $item['quantity'];
            $image_html = $item['image'] ? '<img src="' . esc_url($item['image']) . '" alt="' . esc_attr($item['name']) . '">' : '<div class="no-image">لا توجد صورة</div>';

            // عرض بيانات العميل
            $customer_info_html = '';
            if (!empty($item['customer_data'])) {
                $customer_info_html = '<div class="customer-info-summary">';

                // الاسم الكامل
                if (!empty($item['customer_data']['first_name']) || !empty($item['customer_data']['last_name'])) {
                    $full_name = trim(($item['customer_data']['first_name'] ?? '') . ' ' . ($item['customer_data']['last_name'] ?? ''));
                    if ($full_name) {
                        $customer_info_html .= '<div class="info-item"><strong>العميل:</strong> ' . esc_html($full_name) . '</div>';
                    }
                }

                // الهاتف
                if (!empty($item['customer_data']['phone'])) {
                    $customer_info_html .= '<div class="info-item"><strong>الهاتف:</strong> ' . esc_html($item['customer_data']['phone']) . '</div>';
                }

                // الولاية والبلدية
                $location_parts = array();
                if (!empty($item['customer_data']['municipality'])) {
                    $location_parts[] = $item['customer_data']['municipality'];
                }
                if (!empty($item['customer_data']['state'])) {
                    $location_parts[] = $item['customer_data']['state'];
                }
                if (!empty($item['customer_data']['city'])) {
                    $location_parts[] = $item['customer_data']['city'];
                }

                if (!empty($location_parts)) {
                    $location = implode(', ', $location_parts);
                    $customer_info_html .= '<div class="info-item"><strong>الموقع:</strong> ' . esc_html($location) . '</div>';
                }

                // العنوان التفصيلي
                if (!empty($item['customer_data']['address'])) {
                    $customer_info_html .= '<div class="info-item"><strong>العنوان:</strong> ' . esc_html($item['customer_data']['address']) . '</div>';
                }

                // معلومات إضافية أخرى
                $additional_fields = array(
                    'email' => 'البريد الإلكتروني',
                    'notes' => 'ملاحظات',
                    'company' => 'الشركة',
                    'postal_code' => 'الرمز البريدي'
                );

                foreach ($additional_fields as $field => $label) {
                    if (!empty($item['customer_data'][$field])) {
                        $customer_info_html .= '<div class="info-item"><strong>' . $label . ':</strong> ' . esc_html($item['customer_data'][$field]) . '</div>';
                    }
                }

                // عرض تكلفة التوصيل فقط للمنتج الأول
                if ($index === 0 && $main_shipping_cost > 0) {
                    $customer_info_html .= '<div class="info-item shipping-info"><strong>التوصيل:</strong> ' . number_format($main_shipping_cost, 2) . ' دج</div>';
                }

                $customer_info_html .= '</div>';
            }

            // استخدام المعرف الفعلي للمنتج (يدعم المتغيرات)
            $item_actual_id = isset($item['actual_product_id']) ? $item['actual_product_id'] : $item['product_id'];

            $html .= '
            <div class="cart-item" data-product-id="' . $item_actual_id . '">
                <div class="item-image">' . $image_html . '</div>
                <div class="item-details">
                    <h4 class="item-name">' . esc_html($item['name']) . '</h4>
                    <div class="item-price">' . number_format($item['price'], 2) . ' دج</div>
                    ' . $customer_info_html . '
                    <div class="item-quantity">
                        <button class="qty-btn qty-minus" type="button">-</button>
                        <input type="number" class="qty-input" value="' . $item['quantity'] . '" min="1">
                        <button class="qty-btn qty-plus" type="button">+</button>
                    </div>
                </div>
                <div class="item-total">
                    <div class="total-price">' . number_format($product_total, 2) . ' دج</div>
                    <button class="remove-item" type="button" title="حذف المنتج">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            </div>';
        }

        $html .= '</div>';
        return $html;
    }



    /**
     * معالجة إتمام الطلب
     */
    public function handle_complete_order() {
        if (!wp_verify_nonce($_POST['nonce'], 'custom_cart_nonce')) {
            wp_send_json_error('خطأ في التحقق من الأمان');
            return;
        }

        $cart = $this->get_cart();

        if (empty($cart)) {
            wp_send_json_error('السلة فارغة');
            return;
        }

        // إنشاء طلب جديد
        $order = wc_create_order();

        if (!$order) {
            wp_send_json_error('فشل في إنشاء الطلب');
            return;
        }

        // إضافة المنتجات إلى الطلب
        $customer_data = array();
        $first_item = reset($cart);
        $base_shipping_cost = floatval($first_item['shipping_cost'] ?? 0); // تكلفة التوصيل الأساسية من أول منتج

        // حساب الرسوم الإضافية للفئات
        $additional_shipping_cost = $this->calculate_categories_additional_shipping_cost($cart);
        $total_shipping = $base_shipping_cost + $additional_shipping_cost;

        foreach ($cart as $item) {
            // تحديد المنتج المراد إضافته (المنتج الأساسي أو المتغير)
            $product_to_add = null;
            if (!empty($item['variation_id']) && $item['variation_id'] > 0) {
                // إضافة المتغير
                $product_to_add = wc_get_product($item['variation_id']);
            } else {
                // إضافة المنتج الأساسي
                $product_to_add = wc_get_product($item['product_id']);
            }

            if ($product_to_add) {
                $order->add_product($product_to_add, $item['quantity']);

                // جمع بيانات العميل من أول منتج
                if (empty($customer_data) && !empty($item['customer_data'])) {
                    $customer_data = $item['customer_data'];
                }

                // إضافة بيانات العميل كـ meta data للمنتج
                if (!empty($item['customer_data'])) {
                    $product_meta_key = '_customer_data_' . $item['actual_product_id'];
                    $order->add_meta_data($product_meta_key, $item['customer_data']);
                }
            }
        }

        // إضافة بيانات العميل إلى الطلب
        if (!empty($customer_data)) {
            if (isset($customer_data['first_name'])) {
                $order->set_billing_first_name($customer_data['first_name']);
                $order->set_shipping_first_name($customer_data['first_name']);
            }
            if (isset($customer_data['last_name'])) {
                $order->set_billing_last_name($customer_data['last_name']);
                $order->set_shipping_last_name($customer_data['last_name']);
            }
            if (isset($customer_data['phone'])) {
                $order->set_billing_phone($customer_data['phone']);
            }
            if (isset($customer_data['address'])) {
                $order->set_billing_address_1($customer_data['address']);
                $order->set_shipping_address_1($customer_data['address']);
            }
            if (isset($customer_data['state'])) {
                $order->set_billing_state($customer_data['state']);
                $order->set_shipping_state($customer_data['state']);
            }
            if (isset($customer_data['city'])) {
                $order->set_billing_city($customer_data['city']);
                $order->set_shipping_city($customer_data['city']);
            }
        }

        // إضافة تكلفة التوصيل إذا كانت موجودة
        if ($total_shipping > 0) {
            $shipping_item = new WC_Order_Item_Shipping();

            // تحديد عنوان التوصيل بناءً على وجود رسوم إضافية
            if ($additional_shipping_cost > 0) {
                $shipping_title = sprintf('توصيل (أساسي: %s د.ج + إضافي: %s د.ج)',
                    number_format($base_shipping_cost, 0),
                    number_format($additional_shipping_cost, 0)
                );
            } else {
                $shipping_title = 'توصيل';
            }

            $shipping_item->set_method_title($shipping_title);
            $shipping_item->set_method_id('custom_shipping');
            $shipping_item->set_total($total_shipping);
            $order->add_item($shipping_item);
        }

        // حساب المجاميع
        $order->calculate_totals();

        // تعيين حالة الطلب
        $order->update_status('pending', 'طلب من السلة المخصصة');

        // مسح السلة
        $this->clear_cart();

        wp_send_json_success(array(
            'message' => 'تم إنشاء الطلب بنجاح',
            'order_id' => $order->get_id(),
            'redirect_url' => $order->get_checkout_order_received_url()
        ));
    }



    /**
     * مسح السلة
     */
    private function clear_cart() {
        unset($_SESSION['custom_cart']);
    }
}

// تهيئة الصنف
new Custom_Cart_System();
