<?php

/**
 * اختبار نظام فئات المنتجات ورسوم التوصيل الإضافية
 *
 * @package Form_Elrakami
 * @since 1.0.0
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * صنف اختبار نظام فئات المنتجات ورسوم التوصيل الإضافية
 */
class Categories_Shipping_Test {

    /**
     * تشغيل جميع الاختبارات
     */
    public static function run_all_tests() {
        $results = array();
        
        $results['database_test'] = self::test_database_structure();
        $results['category_manager_test'] = self::test_category_manager();
        $results['shipping_calculation_test'] = self::test_shipping_calculation();
        $results['cart_integration_test'] = self::test_cart_integration();
        
        return $results;
    }

    /**
     * اختبار بنية قاعدة البيانات
     */
    public static function test_database_structure() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'form_elrakami_product_categories_shipping';
        
        // التحقق من وجود الجدول
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        
        if (!$table_exists) {
            return array(
                'status' => 'failed',
                'message' => 'جدول فئات المنتجات غير موجود'
            );
        }

        // التحقق من الأعمدة المطلوبة
        $columns = $wpdb->get_results("DESCRIBE $table_name");
        $required_columns = array('id', 'category_id', 'category_name', 'category_type', 'additional_shipping_cost', 'is_active');
        $existing_columns = array();
        
        foreach ($columns as $column) {
            $existing_columns[] = $column->Field;
        }
        
        $missing_columns = array_diff($required_columns, $existing_columns);
        
        if (!empty($missing_columns)) {
            return array(
                'status' => 'failed',
                'message' => 'أعمدة مفقودة: ' . implode(', ', $missing_columns)
            );
        }

        return array(
            'status' => 'passed',
            'message' => 'بنية قاعدة البيانات صحيحة'
        );
    }

    /**
     * اختبار مدير الفئات
     */
    public static function test_category_manager() {
        if (!class_exists('Product_Categories_Shipping_Manager')) {
            return array(
                'status' => 'failed',
                'message' => 'صنف مدير الفئات غير موجود'
            );
        }

        $manager = new Product_Categories_Shipping_Manager();
        
        // اختبار إضافة فئة تجريبية
        global $wpdb;
        $table_name = $wpdb->prefix . 'form_elrakami_product_categories_shipping';
        
        // إضافة فئة تجريبية
        $test_category_id = 999999; // معرف تجريبي
        $test_cost = 200;
        
        $result = $wpdb->insert(
            $table_name,
            array(
                'category_id' => $test_category_id,
                'category_name' => 'فئة تجريبية',
                'category_type' => 'woocommerce',
                'additional_shipping_cost' => $test_cost,
                'is_active' => 1
            ),
            array('%d', '%s', '%s', '%f', '%d')
        );

        if ($result === false) {
            return array(
                'status' => 'failed',
                'message' => 'فشل في إضافة فئة تجريبية'
            );
        }

        // اختبار استرجاع التكلفة
        $retrieved_cost = $manager->get_category_additional_cost($test_category_id);
        
        // حذف الفئة التجريبية
        $wpdb->delete(
            $table_name,
            array('category_id' => $test_category_id),
            array('%d')
        );

        if ($retrieved_cost != $test_cost) {
            return array(
                'status' => 'failed',
                'message' => 'فشل في استرجاع التكلفة الصحيحة. متوقع: ' . $test_cost . '، مُسترجع: ' . $retrieved_cost
            );
        }

        return array(
            'status' => 'passed',
            'message' => 'مدير الفئات يعمل بشكل صحيح'
        );
    }

    /**
     * اختبار حساب تكلفة التوصيل
     */
    public static function test_shipping_calculation() {
        if (!class_exists('Product_Categories_Shipping_Manager')) {
            return array(
                'status' => 'failed',
                'message' => 'صنف مدير الفئات غير موجود'
            );
        }

        $manager = new Product_Categories_Shipping_Manager();
        
        // إنشاء فئات تجريبية
        global $wpdb;
        $table_name = $wpdb->prefix . 'form_elrakami_product_categories_shipping';
        
        $test_categories = array(
            array('id' => 999998, 'cost' => 150),
            array('id' => 999997, 'cost' => 300)
        );

        foreach ($test_categories as $category) {
            $wpdb->insert(
                $table_name,
                array(
                    'category_id' => $category['id'],
                    'category_name' => 'فئة تجريبية ' . $category['id'],
                    'category_type' => 'woocommerce',
                    'additional_shipping_cost' => $category['cost'],
                    'is_active' => 1
                ),
                array('%d', '%s', '%s', '%f', '%d')
            );
        }

        // محاكاة سلة تحتوي على منتجات من فئات مختلفة
        $mock_cart = array(
            array(
                'product_id' => 1,
                'variation_id' => 0,
                'quantity' => 1,
                'price' => 1000,
                'shipping_cost' => 500
            ),
            array(
                'product_id' => 2,
                'variation_id' => 0,
                'quantity' => 2,
                'price' => 800,
                'shipping_cost' => 500
            )
        );

        // محاكاة دالة wp_get_post_terms
        if (!function_exists('wp_get_post_terms')) {
            function wp_get_post_terms($post_id, $taxonomy, $args = array()) {
                // إرجاع فئات تجريبية بناءً على معرف المنتج
                if ($post_id == 1) {
                    return array(999998); // فئة بتكلفة 150
                } elseif ($post_id == 2) {
                    return array(999997); // فئة بتكلفة 300
                }
                return array();
            }
        }

        $additional_cost = $manager->calculate_cart_additional_shipping_costs($mock_cart);
        $expected_cost = 150 + 300; // مجموع تكلفة الفئتين

        // حذف الفئات التجريبية
        foreach ($test_categories as $category) {
            $wpdb->delete(
                $table_name,
                array('category_id' => $category['id']),
                array('%d')
            );
        }

        if ($additional_cost != $expected_cost) {
            return array(
                'status' => 'failed',
                'message' => 'حساب التكلفة الإضافية غير صحيح. متوقع: ' . $expected_cost . '، محسوب: ' . $additional_cost
            );
        }

        return array(
            'status' => 'passed',
            'message' => 'حساب تكلفة التوصيل يعمل بشكل صحيح'
        );
    }

    /**
     * اختبار تكامل السلة
     */
    public static function test_cart_integration() {
        if (!class_exists('Custom_Cart_System')) {
            return array(
                'status' => 'failed',
                'message' => 'نظام السلة المخصص غير موجود'
            );
        }

        // التحقق من وجود الدالة الجديدة
        $cart_system = new Custom_Cart_System();
        $reflection = new ReflectionClass($cart_system);
        
        if (!$reflection->hasMethod('calculate_categories_additional_shipping_cost')) {
            return array(
                'status' => 'failed',
                'message' => 'دالة حساب الرسوم الإضافية غير موجودة في نظام السلة'
            );
        }

        return array(
            'status' => 'passed',
            'message' => 'تكامل السلة مع نظام الفئات يعمل بشكل صحيح'
        );
    }

    /**
     * عرض نتائج الاختبارات
     */
    public static function display_test_results($results) {
        echo '<div class="wrap">';
        echo '<h1>نتائج اختبار نظام فئات المنتجات ورسوم التوصيل الإضافية</h1>';
        
        $total_tests = count($results);
        $passed_tests = 0;
        
        foreach ($results as $test_name => $result) {
            $status_class = $result['status'] == 'passed' ? 'notice-success' : 'notice-error';
            $status_text = $result['status'] == 'passed' ? 'نجح' : 'فشل';
            
            if ($result['status'] == 'passed') {
                $passed_tests++;
            }
            
            echo '<div class="notice ' . $status_class . '">';
            echo '<p><strong>' . $test_name . ':</strong> ' . $status_text . ' - ' . $result['message'] . '</p>';
            echo '</div>';
        }
        
        echo '<div class="notice notice-info">';
        echo '<p><strong>ملخص:</strong> نجح ' . $passed_tests . ' من أصل ' . $total_tests . ' اختبارات</p>';
        echo '</div>';
        
        if ($passed_tests == $total_tests) {
            echo '<div class="notice notice-success">';
            echo '<p><strong>🎉 تهانينا!</strong> جميع الاختبارات نجحت. النظام جاهز للاستخدام.</p>';
            echo '</div>';
        }
        
        echo '</div>';
    }
}

// إضافة صفحة اختبار في الإدارة
add_action('admin_menu', function() {
    add_submenu_page(
        'form-elrakami-settings',
        'اختبار نظام الفئات',
        'اختبار النظام',
        'manage_options',
        'form-elrakami-test-categories',
        function() {
            if (isset($_GET['run_tests'])) {
                $results = Categories_Shipping_Test::run_all_tests();
                Categories_Shipping_Test::display_test_results($results);
            } else {
                echo '<div class="wrap">';
                echo '<h1>اختبار نظام فئات المنتجات ورسوم التوصيل الإضافية</h1>';
                echo '<p>اضغط على الزر أدناه لتشغيل جميع الاختبارات والتأكد من عمل النظام بشكل صحيح.</p>';
                echo '<a href="?page=form-elrakami-test-categories&run_tests=1" class="button button-primary">تشغيل الاختبارات</a>';
                echo '</div>';
            }
        }
    );
});
