<?php
/**
 * WooCommerce integration class.
 *
 * @link       https://elrakami.com
 * @since      1.0.0
 *
 * @package    Form_Elrakami
 */

/**
 * WooCommerce integration class.
 *
 * This class handles all integration with WooCommerce.
 *
 * @since      1.0.0
 * @package    Form_Elrakami
 */
class Woo_Integration {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Country code for shipping and address defaults.
     * Currently hardcoded to Algeria but can be made configurable.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $default_country    Default country code.
     */
    private $default_country = 'DZ';

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Include the Algeria cities data file
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/algeria-cities.php';

        // Register AJAX handlers for shipping information
        add_action('wp_ajax_form_elrakami_get_states', array($this, 'ajax_get_states'));
        add_action('wp_ajax_nopriv_form_elrakami_get_states', array($this, 'ajax_get_states'));

        add_action('wp_ajax_form_elrakami_get_cities', array($this, 'ajax_get_cities'));
        add_action('wp_ajax_nopriv_form_elrakami_get_cities', array($this, 'ajax_get_cities'));

        add_action('wp_ajax_form_elrakami_get_shipping_methods', array($this, 'ajax_get_shipping_methods'));
        add_action('wp_ajax_nopriv_form_elrakami_get_shipping_methods', array($this, 'ajax_get_shipping_methods'));

        // Register AJAX handler for product settings
        add_action('wp_ajax_form_elrakami_get_product_settings', array($this, 'ajax_get_product_settings'));
        add_action('wp_ajax_nopriv_form_elrakami_get_product_settings', array($this, 'ajax_get_product_settings'));
        

    }

    /**
     * Helper method to prevent "Commands out of sync" database errors
     * Frees any unfetched results from previous database queries
     *
     * @since    1.0.0
     * @return   void
     */
    public function free_results() {
        Form_Elrakami_Helper::free_results();
    }

    /**
     * AJAX handler for getting product settings
     *
     * @since    1.0.0
     * @return   void
     */
    public function ajax_get_product_settings() {
        // التحقق من الأمان
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'form_elrakami_nonce')) {
            wp_send_json_error('فشل التحقق الأمني');
            exit;
        }

        // التحقق من وجود معرف المنتج
        if (!isset($_POST['product_id']) || empty($_POST['product_id'])) {
            wp_send_json_error('معرف المنتج غير موجود');
            exit;
        }

        $product_id = intval($_POST['product_id']);

        // الحصول على إعدادات المنتج
        $settings = array(
            'price_display_type' => get_post_meta($product_id, '_price_display_type', true) ?: 'range'
        );

        wp_send_json_success($settings);
        exit;
    }

    /**
     * Add form after product title.
     * This method adds the form directly after the product title.
     *
     * @since    1.0.0
     */
    public function add_form_after_product_title() {
        // We need to be on a product page
        if (!is_product()) {
            return;
        }

        // Get the product
        global $product;
        if (!$product) {
            $product = wc_get_product(get_the_ID());
            if (!$product) {
                return;
            }
        }

        // التحقق مما إذا كان النموذج معطلاً لهذا المنتج
        $disable_form = get_post_meta($product->get_id(), '_form_elrakami_disable_form', true);
        if ($disable_form === 'yes') {
            return;
        }

        // Get default form ID from options
        $default_form_id = get_option('form_elrakami_default_form_id', 0);

        // If no default form is selected, return
        if (empty($default_form_id)) {
            return;
        }

        // Check if this product has a specific form assigned
        $product_form_id = get_post_meta($product->get_id(), '_form_elrakami_form_id', true);
        $form_id = !empty($product_form_id) ? $product_form_id : $default_form_id;

        // Get form from database
        global $wpdb;
        $table_name = $wpdb->prefix . 'form_elrakami_forms';
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d AND status = 'active'", $form_id));

        // If form is not found or inactive, return
        if (!$form) {
            return;
        }

        // Get form settings and fields
        $settings = Form_Elrakami_Helper::unserialize_data($form->settings);
        $fields = Form_Elrakami_Helper::unserialize_data($form->fields);

        // Prepare form data for template
        $form_data = array(
            'form_id' => $form->id,
            'settings' => array(
                'form_settings' => $settings,
                'fields' => $fields,
            ),
            'product_id' => $product->get_id(),
        );

        // Include the form template directly without additional container
        include plugin_dir_path(dirname(__FILE__)) . 'public/partials/form-template.php';
    }

    /**
     * Save form data to order.
     *
     * @since    1.0.0
     * @param    WC_Order    $order    The order object.
     * @param    array       $data     The checkout form data.
     */
    public function save_form_data_to_order($order, $data) {
        $saved_form_data = array();

        // Check cart items for form data
        $items = $order->get_items();
        foreach ($items as $item) {
            $product_id = $item->get_product_id();

            // Get form data from cart item
            $form_data = $this->get_form_data_from_cart($product_id);

            if ($form_data && !empty($form_data)) {
                // Save form data for this product
                $saved_form_data[$product_id] = $form_data;

                // Save form data to item meta
                $item->add_meta_data('_form_elrakami_data', $form_data);
                $item->save();

                // Add individual form fields as separate meta for easier access
                if (is_array($form_data)) {
                    foreach ($form_data as $field_id => $field_data) {
                        // Skip non-field data
                        if ($field_id === 'form_id' || $field_id === 'action' || $field_id === 'nonce' || $field_id === 'product_id') {
                            continue;
                        }

                        if (isset($field_data['label']) && isset($field_data['value'])) {
                            $meta_key = sanitize_title('form_' . $field_id);
                            $item->add_meta_data($meta_key, $field_data['value']);

                            // Also add with label as key for better readability in admin
                            $label_key = sanitize_title($field_data['label']);
                            $item->add_meta_data($label_key, $field_data['value']);
                        }
                    }
                    $item->save();
                }

                // Save to form submissions table
                $this->save_submission_to_database($form_data, $order->get_id());
            }
        }

        // If we have form data for any product, save it to order meta
        if (!empty($saved_form_data)) {
            $order->update_meta_data('_form_elrakami_data', $saved_form_data);
        }

        // Check for form data in session as fallback
        if (WC()->session && WC()->session->get('form_elrakami_data')) {
            $session_form_data = WC()->session->get('form_elrakami_data');

            // Save session form data if we don't already have form data for this product
            $product_id = isset($session_form_data['product_id']) ? intval($session_form_data['product_id']) : 0;

            if ($product_id > 0 && !isset($saved_form_data[$product_id])) {
                // Add to order meta
                if (empty($saved_form_data)) {
                    $saved_form_data = array();
                }
                $saved_form_data[$product_id] = $session_form_data;
                $order->update_meta_data('_form_elrakami_data', $saved_form_data);

                // Save to form submissions table
                $this->save_submission_to_database($session_form_data, $order->get_id());
            }

            // Clear session data
            WC()->session->set('form_elrakami_data', null);
        }
    }



    /**
     * Save form submission to database.
     *
     * @since    1.0.0
     * @param    array    $form_data    The form data.
     * @param    int      $order_id     The order ID.
     */
    private function save_submission_to_database($form_data, $order_id) {
        global $wpdb;

        // Get form ID
        $form_id = isset($form_data['form_id']) ? intval($form_data['form_id']) : 0;

        // Get user ID if logged in
        $user_id = get_current_user_id();

        // Free results to prevent "Commands out of sync" errors
        $this->free_results();

        // Insert submission
        $wpdb->insert(
            $wpdb->prefix . 'form_elrakami_submissions',
            array(
                'form_id' => $form_id,
                'data' => Form_Elrakami_Helper::serialize_data($form_data),
                'order_id' => $order_id,
                'user_id' => $user_id > 0 ? $user_id : null,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql'),
            ),
            array('%d', '%s', '%d', '%d', '%s', '%s')
        );
    }



    /**
     * Display form data in admin order view.
     *
     * @since    1.0.0
     * @param    WC_Order    $order    The order object.
     */
    public function display_form_data_in_admin_order($order) {
        $form_data = $order->get_meta('_form_elrakami_data');

        if (!$form_data || !is_array($form_data)) {
            return;
        }

        echo '<h3>معلومات العميل الإضافية</h3>';
        echo '<div class="form-elrakami-order-data">';

        // Check if form data is organized by product ID
        if ($this->is_multidimensional_array($form_data)) {
            // Form data is organized by product ID
            foreach ($form_data as $product_id => $product_form_data) {
                // Get product name
                $product = wc_get_product($product_id);
                $product_name = $product ? $product->get_name() : sprintf('المنتج #%d', $product_id);

                echo '<h4>' . esc_html($product_name) . '</h4>';
                echo '<table class="widefat fixed" style="margin-bottom: 20px;">';

                $this->render_form_data_table($product_form_data);

                echo '</table>';
            }
        } else {
            // Old format - single form data
            echo '<table class="widefat fixed" style="margin-bottom: 20px;">';
            $this->render_form_data_table($form_data);
            echo '</table>';
        }

        echo '</div>';
    }

    /**
     * Render form data as table rows.
     *
     * @since    1.0.0
     * @param    array    $form_data    The form data.
     */
    private function render_form_data_table($form_data) {
        foreach ($form_data as $field_id => $field_data) {
            // Skip non-field data
            if (in_array($field_id, array('form_id', 'action', 'nonce', 'product_id'))) {
                continue;
            }

            // Handle different data formats
            if (is_array($field_data) && isset($field_data['label']) && isset($field_data['value'])) {
                // New format with label and value
                $label = $field_data['label'];
                $value = $field_data['value'];

                if (is_array($value)) {
                    $value = implode(', ', $value);
                }

                echo '<tr>';
                echo '<th style="width: 30%; padding: 8px;">' . esc_html($label) . ':</th>';
                echo '<td style="padding: 8px;">' . esc_html($value) . '</td>';
                echo '</tr>';
            } elseif (!is_array($field_data)) {
                // Simple key-value format
                echo '<tr>';
                echo '<th style="width: 30%; padding: 8px;">' . esc_html($field_id) . ':</th>';
                echo '<td style="padding: 8px;">' . esc_html($field_data) . '</td>';
                echo '</tr>';
            }
        }
    }

    /**
     * Check if an array is multidimensional (has product IDs as keys).
     *
     * @since    1.0.0
     * @param    array    $array    The array to check.
     * @return   bool               True if multidimensional, false otherwise.
     */
    private function is_multidimensional_array($array) {
        if (!is_array($array)) {
            return false;
        }

        // Check if first key is numeric (product ID)
        reset($array);
        $first_key = key($array);

        return is_numeric($first_key) && is_array($array[$first_key]);
    }

    /**
     * Display form data in customer order emails.
     *
     * @since    1.0.0
     * @param    WC_Order    $order           The order object.
     * @param    bool        $sent_to_admin   Sent to admin or not.
     * @param    bool        $plain_text      Plain text or HTML.
     * @param    WC_Email    $email           The email object.
     */
    public function display_form_data_in_emails($order, $sent_to_admin, $plain_text, $email) {
        $form_data = $order->get_meta('_form_elrakami_data');

        if (!$form_data || !is_array($form_data)) {
            return;
        }

        if ($plain_text) {
            // Plain text format
            echo "معلومات العميل الإضافية:\n\n";

            // Check if form data is organized by product ID
            if ($this->is_multidimensional_array($form_data)) {
                // Form data is organized by product ID
                foreach ($form_data as $product_id => $product_form_data) {
                    // Get product name
                    $product = wc_get_product($product_id);
                    $product_name = $product ? $product->get_name() : sprintf('المنتج #%d', $product_id);

                    echo esc_html($product_name) . ":\n";
                    $this->render_plain_text_form_data($product_form_data);
                    echo "\n";
                }
            } else {
                // Old format - single form data
                $this->render_plain_text_form_data($form_data);
            }
        } else {
            // HTML format
            echo '<h2>معلومات العميل الإضافية</h2>';

            // Check if form data is organized by product ID
            if ($this->is_multidimensional_array($form_data)) {
                // Form data is organized by product ID
                foreach ($form_data as $product_id => $product_form_data) {
                    // Get product name
                    $product = wc_get_product($product_id);
                    $product_name = $product ? $product->get_name() : sprintf('المنتج #%d', $product_id);

                    echo '<h3 style="margin-top: 15px;">' . esc_html($product_name) . '</h3>';
                    echo '<table class="td" cellspacing="0" cellpadding="6" style="width: 100%; margin-bottom: 15px;">';
                    $this->render_html_email_form_data($product_form_data);
                    echo '</table>';
                }
            } else {
                // Old format - single form data
                echo '<table class="td" cellspacing="0" cellpadding="6" style="width: 100%; margin-bottom: 20px;">';
                $this->render_html_email_form_data($form_data);
                echo '</table>';
            }
        }
    }

    /**
     * Render form data as plain text for emails.
     *
     * @since    1.0.0
     * @param    array    $form_data    The form data.
     */
    private function render_plain_text_form_data($form_data) {
        foreach ($form_data as $field_id => $field_data) {
            // Skip non-field data
            if (in_array($field_id, array('form_id', 'action', 'nonce', 'product_id'))) {
                continue;
            }

            // Handle different data formats
            if (is_array($field_data) && isset($field_data['label']) && isset($field_data['value'])) {
                // New format with label and value
                $label = $field_data['label'];
                $value = $field_data['value'];

                if (is_array($value)) {
                    $value = implode(', ', $value);
                }

                echo esc_html($label) . ': ' . esc_html($value) . "\n";
            } elseif (!is_array($field_data)) {
                // Simple key-value format
                echo esc_html($field_id) . ': ' . esc_html($field_data) . "\n";
            }
        }
    }

    /**
     * Render form data as HTML table rows for emails.
     *
     * @since    1.0.0
     * @param    array    $form_data    The form data.
     */
    private function render_html_email_form_data($form_data) {
        foreach ($form_data as $field_id => $field_data) {
            // Skip non-field data
            if (in_array($field_id, array('form_id', 'action', 'nonce', 'product_id'))) {
                continue;
            }

            // Handle different data formats
            if (is_array($field_data) && isset($field_data['label']) && isset($field_data['value'])) {
                // New format with label and value
                $label = $field_data['label'];
                $value = $field_data['value'];

                if (is_array($value)) {
                    $value = implode(', ', $value);
                }

                echo '<tr>';
                echo '<th style="text-align: right; width: 30%;">' . esc_html($label) . ':</th>';
                echo '<td style="text-align: right;">' . esc_html($value) . '</td>';
                echo '</tr>';
            } elseif (!is_array($field_data)) {
                // Simple key-value format
                echo '<tr>';
                echo '<th style="text-align: right; width: 30%;">' . esc_html($field_id) . ':</th>';
                echo '<td style="text-align: right;">' . esc_html($field_data) . '</td>';
                echo '</tr>';
            }
        }
    }

    /**
     * Display form data in order details on my account page.
     *
     * @since    1.0.0
     * @param    WC_Order    $order    The order object.
     */
    public function display_form_data_in_order_details($order) {
        $form_data = $order->get_meta('_form_elrakami_data');

        if (!$form_data || !is_array($form_data)) {
            return;
        }

        echo '<h2>معلومات إضافية</h2>';

        // Check if form data is organized by product ID
        if ($this->is_multidimensional_array($form_data)) {
            // Form data is organized by product ID
            foreach ($form_data as $product_id => $product_form_data) {
                // Get product name
                $product = wc_get_product($product_id);
                $product_name = $product ? $product->get_name() : sprintf('المنتج #%d', $product_id);

                echo '<h3>' . esc_html($product_name) . '</h3>';
                echo '<table class="woocommerce-table shop_table form-elrakami-data">';

                foreach ($product_form_data as $field_id => $field_data) {
                    // Skip non-field data
                    if (in_array($field_id, array('form_id', 'action', 'nonce', 'product_id'))) {
                        continue;
                    }

                    // Handle different data formats
                    if (is_array($field_data) && isset($field_data['label']) && isset($field_data['value'])) {
                        // New format with label and value
                        $label = $field_data['label'];
                        $value = $field_data['value'];

                        if (is_array($value)) {
                            $value = implode(', ', $value);
                        }

                        echo '<tr>';
                        echo '<th>' . esc_html($label) . ':</th>';
                        echo '<td>' . esc_html($value) . '</td>';
                        echo '</tr>';
                    } elseif (!is_array($field_data)) {
                        // Simple key-value format
                        echo '<tr>';
                        echo '<th>' . esc_html($field_id) . ':</th>';
                        echo '<td>' . esc_html($field_data) . '</td>';
                        echo '</tr>';
                    }
                }

                echo '</table>';
            }
        } else {
            // Old format - single form data
            echo '<table class="woocommerce-table shop_table form-elrakami-data">';

            foreach ($form_data as $field_id => $field_data) {
                // Skip non-field data
                if (in_array($field_id, array('form_id', 'action', 'nonce', 'product_id'))) {
                    continue;
                }

                // Handle different data formats
                if (is_array($field_data) && isset($field_data['label']) && isset($field_data['value'])) {
                    // New format with label and value
                    echo '<tr>';
                    echo '<th>' . esc_html($field_data['label']) . ':</th>';

                    if (is_array($field_data['value'])) {
                        echo '<td>' . esc_html(implode(', ', $field_data['value'])) . '</td>';
                    } else {
                        echo '<td>' . esc_html($field_data['value']) . '</td>';
                    }

                    echo '</tr>';
                } elseif (!is_array($field_data)) {
                    // Simple key-value format
                    echo '<tr>';
                    echo '<th>' . esc_html($field_id) . ':</th>';
                    echo '<td>' . esc_html($field_data) . '</td>';
                    echo '</tr>';
                }
            }

            echo '</table>';
        }
    }

    /**
     * AJAX handler for getting states.
     *
     * @since    1.0.0
     * @return   void
     */
    public function ajax_get_states() {
        // WooCommerce uses 'billing_country' for country code
        $country = isset($_POST['country']) ? sanitize_text_field($_POST['country']) : $this->default_country;

        // Free results to prevent "Commands out of sync" errors
        $this->free_results();

        // For Algeria, always use our complete 58 states list instead of WooCommerce's limited 48 states
        // This ensures all 58 Algerian states are available in the forms, sorted from 1 to 58
        if ($country === 'DZ') {
            $states = $this->get_default_states($country);
        } else {
            // For other countries, use WooCommerce states
            $states = WC()->countries->get_states($country);

            if (empty($states)) {
                // Fallback to default states if WooCommerce doesn't have any for this country
                $states = $this->get_default_states($country);
            }
        }

        wp_send_json_success($states);
        exit;
    }

    /**
     * AJAX handler for getting cities based on state.
     *
     * @since    1.0.0
     * @return   void
     */
    public function ajax_get_cities() {
        $country = isset($_POST['country']) ? sanitize_text_field($_POST['country']) : $this->default_country;
        $state = isset($_POST['state']) ? sanitize_text_field($_POST['state']) : '';

        if (empty($state)) {
            wp_send_json_error('لم يتم تحديد الولاية');
            exit;
        }

        // Free results to prevent "Commands out of sync" errors
        $this->free_results();

        // Get cities for this state
        $cities = $this->get_cities_for_state($country, $state);

        // تسجيل المعلومات في السجل للتشخيص
        error_log('طلب المدن للولاية: ' . $state);
        error_log('عدد المدن المسترجعة: ' . count($cities));

        wp_send_json_success($cities);
        exit;
    }

    /**
     * AJAX handler for getting shipping methods.
     *
     * @since    1.0.0
     * @return   void
     */
    public function ajax_get_shipping_methods() {
        $country = isset($_POST['country']) ? sanitize_text_field($_POST['country']) : $this->default_country;
        $state = isset($_POST['state']) ? sanitize_text_field($_POST['state']) : '';
        $city = isset($_POST['municipality']) ? sanitize_text_field($_POST['municipality']) :
               (isset($_POST['city']) ? sanitize_text_field($_POST['city']) : '');
        $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

        if ($product_id <= 0) {
            wp_send_json_error('لم يتم تحديد المنتج');
            exit;
        }

        // Free results to prevent "Commands out of sync" errors
        $this->free_results();

        // Get shipping methods for this location
        $shipping_methods = $this->get_shipping_methods_for_location($country, $state, $city, $product_id);

        wp_send_json_success($shipping_methods);
        exit;
    }

    /**
     * Get default states for a country.
     * This is used when WooCommerce doesn't have states defined for the country.
     *
     * @since    1.0.0
     * @param    string   $country    Country code.
     * @return   array                States as key => value pairs.
     */
    private function get_default_states($country) {
        // Default states for Algeria - use the optimized function
        if ($country === 'DZ') {
            return get_algeria_states();
        }

        // Return empty array for unsupported countries
        return array();
    }

    /**
     * Get cities for a state.
     * Currently, we'll implement a simple demo with major cities for each state.
     * In production, this would be replaced with a proper cities database.
     *
     * @since    1.0.0
     * @param    string   $country    Country code.
     * @param    string   $state      State code.
     * @return   array                Cities list.
     */
    private function get_cities_for_state($country, $state) {
        // For Algeria, use our comprehensive cities list
        if ($country === 'DZ') {
            // تعديل رمز الولاية إذا كان رقماً من منزلة واحدة
            if (is_numeric($state) && strlen($state) === 1) {
                $old_state = $state;
                $state = '0' . $state;
                error_log('تم تعديل رمز الولاية في get_cities_for_state من ' . $old_state . ' إلى ' . $state);
            }

            // تسجيل معلومات طلب البلديات
            error_log('طلب البلديات للولاية: ' . $state);

            // First check if we have a custom cities list in a cached transient
            $transient_key = 'form_elrakami_cities_' . $country . '_' . sanitize_title($state);
            $cities = get_transient($transient_key);

            if (false !== $cities) {
                error_log('تم استرجاع البلديات من الذاكرة المؤقتة، عدد البلديات: ' . count($cities));
                return $cities;
            }

            // Get municipalities from our data file
            $municipalities = get_municipalities_for_state($state);

            // تسجيل عدد البلديات المسترجعة
            error_log('عدد البلديات المسترجعة للولاية ' . $state . ': ' . count($municipalities));

            // Initialize cities array
            $cities = array();

            // Add all municipalities for this state
            foreach ($municipalities as $index => $city_name) {
                // Generate a unique key for each city
                $city_key = sanitize_title($city_name);
                $cities[$city_key] = $city_name;
            }

            // If no cities were found, provide a default for backward compatibility
            if (empty($cities)) {
                error_log('لم يتم العثور على بلديات للولاية ' . $state . '، سيتم استخدام الافتراضي');
                $cities = array(
                    sanitize_title($state) => $state
                );
            }

            // Cache the cities list
            set_transient($transient_key, $cities, WEEK_IN_SECONDS);

            return $cities;
        }

        // Return empty array for unsupported countries
        return array();
    }

    /**
     * Get shipping methods for a specific location.
     * Uses WooCommerce's shipping zone system to get appropriate shipping methods.
     *
     * @since    1.0.0
     * @param    string   $country      Country code.
     * @param    string   $state        State code.
     * @param    string   $city         City name.
     * @param    int      $product_id   Product ID.
     * @return   array                  Shipping methods as array of objects with rate_id, method_id, title, cost
     */
    private function get_shipping_methods_for_location($country, $state, $city, $product_id) {
        $shipping_methods = array();

        error_log("Form Elrakami: بداية طلب طرق الشحن");
        error_log("Form Elrakami: البلد: $country");
        error_log("Form Elrakami: الولاية: $state");
        error_log("Form Elrakami: المدينة: $city");
        error_log("Form Elrakami: معرف المنتج: $product_id");

        // Get product
        $product = wc_get_product($product_id);
        if (!$product) {
            error_log("Form Elrakami: المنتج غير موجود، استخدام الطرق الافتراضية");
            return $this->get_default_shipping_methods($country, $state);
        }

        // Try to get WooCommerce shipping methods first
        $wc_shipping_methods = $this->get_woocommerce_shipping_methods($country, $state, $city, $product);

        if (!empty($wc_shipping_methods)) {
            error_log("Form Elrakami: تم العثور على " . count($wc_shipping_methods) . " طرق شحن من WooCommerce");
            return $wc_shipping_methods;
        }

        // Fallback to default methods
        error_log("Form Elrakami: لم يتم العثور على طرق شحن من WooCommerce، استخدام الطرق الافتراضية");
        return $this->get_default_shipping_methods($country, $state);
    }

    /**
     * Get WooCommerce shipping methods for location
     */
    private function get_woocommerce_shipping_methods($country, $state, $city, $product) {
        $shipping_methods = array();

        try {
            // تحويل كود الولاية إلى التنسيق الصحيح لـ WooCommerce
            $wc_state_code = $this->convert_state_code_for_woocommerce($state);

            error_log("Form Elrakami: تحويل كود الولاية - الكود الأصلي: $state");
            error_log("Form Elrakami: تحويل كود الولاية - الكود المحول: $wc_state_code");

            // التحقق من وجود المنتج
            if (!$product) {
                error_log("Form Elrakami: خطأ - المنتج غير موجود");
                return array();
            }

            // Create package for shipping calculation
            $package = array(
                'contents' => array(
                    $product->get_id() => array(
                        'product_id' => $product->get_id(),
                        'variation_id' => 0,
                        'quantity' => 1,
                        'data' => $product,
                        'line_total' => $product->get_price(),
                        'line_tax' => 0,
                        'line_subtotal' => $product->get_price(),
                        'line_subtotal_tax' => 0
                    )
                ),
                'contents_cost' => $product->get_price(),
                'applied_coupons' => array(),
                'user' => array('ID' => get_current_user_id()),
                'destination' => array(
                    'country' => $country,
                    'state' => $wc_state_code, // استخدام الكود المحول
                    'postcode' => '',
                    'city' => $city,
                    'address' => '',
                    'address_2' => ''
                )
            );

            error_log("Form Elrakami: Package destination - Country: $country, State: $wc_state_code, City: $city");

            // Get shipping for this package
            error_log("Form Elrakami: بداية حساب طرق الشحن لـ WooCommerce");
            error_log("Form Elrakami: Package Data - " . json_encode($package));

            $shipping_methods_objects = WC()->shipping()->calculate_shipping_for_package($package);

            error_log("Form Elrakami: نتيجة حساب طرق الشحن - " . json_encode($shipping_methods_objects));
            error_log("Form Elrakami: عدد طرق الشحن المسترجعة: " . count($shipping_methods_objects['rates'] ?? []));

            if (empty($shipping_methods_objects['rates'])) {
                error_log("Form Elrakami: لم يتم العثور على طرق شحن. سيتم استخدام الطرق الافتراضية.");
            }

            // Format shipping methods
            if (!empty($shipping_methods_objects['rates'])) {
                foreach ($shipping_methods_objects['rates'] as $rate_id => $rate) {
                    $shipping_methods[] = array(
                        'rate_id' => $rate_id,
                        'method_id' => $rate->get_method_id(),
                        'title' => $rate->get_label(),
                        'cost' => floatval($rate->get_cost()),
                        'formatted_cost' => wc_price($rate->get_cost()),
                        'description' => $rate->get_description(),
                        'taxes' => $rate->get_taxes(),
                        'meta_data' => $rate->get_meta_data()
                    );
                }

                error_log("Form Elrakami: تم تنسيق " . count($shipping_methods) . " طرق شحن من WooCommerce");
            }
        } catch (Exception $e) {
            // Log error but don't break the process
            error_log('Form Elrakami: Error getting WooCommerce shipping methods - ' . $e->getMessage());
        }

        return $shipping_methods;
    }

    /**
     * تحويل كود الولاية إلى التنسيق المطلوب لـ WooCommerce
     */
    private function convert_state_code_for_woocommerce($state_code) {
        // إذا كان الكود بالفعل بتنسيق DZ-XX، أرجعه كما هو
        if (strpos($state_code, 'DZ-') === 0) {
            return $state_code;
        }

        // إذا كان الكود رقماً فقط (01, 02, إلخ)، حوله إلى DZ-XX
        if (is_numeric($state_code) || preg_match('/^\d{1,2}$/', $state_code)) {
            $state_number = intval($state_code);
            return 'DZ-' . sprintf('%02d', $state_number);
        }

        // إذا كان الكود بتنسيق آخر، حاول استخراج الرقم
        if (preg_match('/(\d{1,2})/', $state_code, $matches)) {
            $state_number = intval($matches[1]);
            return 'DZ-' . sprintf('%02d', $state_number);
        }

        // إذا فشل كل شيء، أرجع الكود كما هو
        return $state_code;
    }



    /**
     * Get default shipping methods when WooCommerce zones don't match.
     *
     * @since    1.0.0
     * @param    string   $country   Country code.
     * @param    string   $state     State code.
     * @return   array               Default shipping methods for location.
     */
    private function get_default_shipping_methods($country, $state) {
        // الحصول على إعدادات النموذج
        $form_settings = get_option('form_elrakami_settings', array());

        // الحصول على أسماء وأسعار طرق الشحن من الإعدادات
        $shipping_1_title = isset($form_settings['default_shipping_1_title']) ? $form_settings['default_shipping_1_title'] : 'توصيل للمنزل';
        $shipping_1_cost = isset($form_settings['default_shipping_1_cost']) ? intval($form_settings['default_shipping_1_cost']) : 800;
        $shipping_1_description = isset($form_settings['default_shipping_1_description']) ? $form_settings['default_shipping_1_description'] : '';
        $shipping_1_enabled = isset($form_settings['default_shipping_1_enabled']) ? intval($form_settings['default_shipping_1_enabled']) : 1;

        $shipping_2_title = isset($form_settings['default_shipping_2_title']) ? $form_settings['default_shipping_2_title'] : 'توصيل للمكتب';
        $shipping_2_cost = isset($form_settings['default_shipping_2_cost']) ? intval($form_settings['default_shipping_2_cost']) : 600;
        $shipping_2_description = isset($form_settings['default_shipping_2_description']) ? $form_settings['default_shipping_2_description'] : '';
        $shipping_2_enabled = isset($form_settings['default_shipping_2_enabled']) ? intval($form_settings['default_shipping_2_enabled']) : 1;

        $shipping_methods = array();

        // If this is Algeria, provide meaningful defaults for specific states
        if ($country === 'DZ') {
            // Get cached shipping methods for state
            $transient_key = 'form_elrakami_shipping_' . $country . '_' . sanitize_title($state);
            $methods = get_transient($transient_key);

            if (false !== $methods) {
                return $methods;
            }

            // Define default shipping methods for specific states
            switch (strtoupper($state)) {
                case 'ALGER':
                    $methods = array(
                        array(
                            'title' => 'توصيل سريع - الجزائر',
                            'cost' => 500,
                            'description' => 'توصيل في نفس اليوم للجزائر العاصمة'
                        ),
                        array(
                            'title' => 'توصيل عادي - الجزائر',
                            'cost' => 300,
                            'description' => 'توصيل خلال 1-2 أيام'
                        )
                    );
                    break;

                case 'ORAN':
                case 'CONSTANTINE':
                case 'ANNABA':
                    $methods = array(
                        array(
                            'title' => 'توصيل سريع - المدن الكبرى',
                            'cost' => 700,
                            'description' => 'توصيل خلال 1-2 أيام'
                        ),
                        array(
                            'title' => 'توصيل عادي - المدن الكبرى',
                            'cost' => 500,
                            'description' => 'توصيل خلال 2-3 أيام'
                        )
                    );
                    break;

                default:
                    // استخدام طرق الشحن الافتراضية المخصصة
                    $methods = array();

                    // إضافة طريقة الشحن الأولى إذا كانت مفعلة
                    if ($shipping_1_enabled) {
                        $methods[] = array(
                            'rate_id' => 'default_' . sanitize_title($shipping_1_title),
                            'method_id' => 'default',
                            'title' => $shipping_1_title,
                            'cost' => floatval($shipping_1_cost),
                            'formatted_cost' => wc_price($shipping_1_cost),
                            'description' => $shipping_1_description,
                            'taxes' => array(),
                            'meta_data' => array()
                        );
                    }

                    // إضافة طريقة الشحن الثانية إذا كانت مفعلة
                    if ($shipping_2_enabled) {
                        $methods[] = array(
                            'rate_id' => 'default_' . sanitize_title($shipping_2_title),
                            'method_id' => 'default',
                            'title' => $shipping_2_title,
                            'cost' => floatval($shipping_2_cost),
                            'formatted_cost' => wc_price($shipping_2_cost),
                            'description' => $shipping_2_description,
                            'taxes' => array(),
                            'meta_data' => array()
                        );
                    }

                    // إذا لم تكن هناك طرق شحن مفعلة، إضافة طريقة افتراضية
                    if (empty($methods)) {
                        $methods[] = array(
                            'rate_id' => 'default_standard',
                            'method_id' => 'default',
                            'title' => 'توصيل قياسي',
                            'cost' => 0,
                            'formatted_cost' => wc_price(0),
                            'description' => 'توصيل مجاني',
                            'taxes' => array(),
                            'meta_data' => array()
                        );
                    }
                    break;
            }

            // Cache shipping methods - تقليل مدة التخزين المؤقت لإظهار التغييرات بسرعة أكبر
            set_transient($transient_key, $methods, HOUR_IN_SECONDS);

            return $methods;
        }

        // استخدام طرق الشحن الافتراضية المخصصة لأي بلد آخر
        // إضافة طريقة الشحن الأولى إذا كانت مفعلة
        if ($shipping_1_enabled) {
            $shipping_methods[] = array(
                'rate_id' => 'default_' . sanitize_title($shipping_1_title),
                'method_id' => 'default',
                'title' => $shipping_1_title,
                'cost' => floatval($shipping_1_cost),
                'formatted_cost' => wc_price($shipping_1_cost),
                'description' => $shipping_1_description,
                'taxes' => array(),
                'meta_data' => array()
            );
        }

        // إضافة طريقة الشحن الثانية إذا كانت مفعلة
        if ($shipping_2_enabled) {
            $shipping_methods[] = array(
                'rate_id' => 'default_' . sanitize_title($shipping_2_title),
                'method_id' => 'default',
                'title' => $shipping_2_title,
                'cost' => floatval($shipping_2_cost),
                'formatted_cost' => wc_price($shipping_2_cost),
                'description' => $shipping_2_description,
                'taxes' => array(),
                'meta_data' => array()
            );
        }

        // إذا لم تكن هناك طرق شحن مفعلة، إضافة طريقة افتراضية
        if (empty($shipping_methods)) {
            $shipping_methods[] = array(
                'rate_id' => 'default_standard',
                'method_id' => 'default',
                'title' => 'توصيل قياسي',
                'cost' => 0,
                'formatted_cost' => wc_price(0),
                'description' => 'توصيل مجاني',
                'taxes' => array(),
                'meta_data' => array()
            );
        }

        return $shipping_methods;
    }

    /**
     * Clear cached states and cities data.
     * Useful when updating the states/cities data structure.
     *
     * @since    1.0.0
     * @return   void
     */
    public function clear_states_cache() {
        global $wpdb;

        // Delete all transients related to states and cities
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_form_elrakami_cities_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_form_elrakami_cities_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_form_elrakami_shipping_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_form_elrakami_shipping_%'");

        // Clear WooCommerce cache if available
        if (function_exists('wc_delete_shipping_zone_cache')) {
            wc_delete_shipping_zone_cache();
        }
    }
    

        

        

    }