<?php
/**
 * Fired during plugin activation
 *
 * @link       https://elrakami.com
 * @since      1.0.0
 *
 * @package    Form_Elrakami
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.0.0
 * @package    Form_Elrakami
 */
class Form_Elrakami_Activator {

    /**
     * Create plugin tables and initialize required data.
     *
     * @since    1.0.0
     */
    public static function activate() {
        global $wpdb;

        // Set up character set
        $charset_collate = $wpdb->get_charset_collate();

        // Define table names
        $forms_table = $wpdb->prefix . 'form_elrakami_forms';
        $blocked_customers_table = $wpdb->prefix . 'form_elrakami_blocked_customers';

        // SQL for creating forms table
        $forms_sql = "CREATE TABLE IF NOT EXISTS $forms_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description text,
            fields longtext,
            settings longtext,
            status varchar(20) NOT NULL DEFAULT 'active',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id)
        ) $charset_collate;";

        // No longer using submissions table

        // SQL for creating blocked customers table
        $blocked_customers_sql = "CREATE TABLE IF NOT EXISTS $blocked_customers_table (
            id int(11) NOT NULL AUTO_INCREMENT,
            customer_name varchar(255),
            phone_number varchar(50),
            ip_address varchar(45),
            email varchar(255),
            reason text,
            blocked_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            blocked_by bigint(20),
            status varchar(20) NOT NULL DEFAULT 'active',
            notes text,
            PRIMARY KEY  (id),
            KEY phone_number (phone_number),
            KEY ip_address (ip_address),
            KEY email (email),
            KEY status (status)
        ) $charset_collate;";

        // Include WordPress database upgrade functions
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        // Create tables
        dbDelta($forms_sql);
        dbDelta($blocked_customers_sql);

        // Create default form if no forms exist
        $form_count = $wpdb->get_var("SELECT COUNT(*) FROM $forms_table");

        if ($form_count == 0) {
            // Default fields for product form
            $default_fields = array(
                array(
                    'id' => 'full_name',
                    'label' => 'الاسم الكامل',
                    'type' => 'text',
                    'required' => true,
                    'visible' => true,
                    'options' => array(),
                    'placeholder' => '',
                    'default_value' => '',
                    'description' => '',
                ),
                array(
                    'id' => 'phone',
                    'label' => 'رقم الهاتف',
                    'type' => 'tel',
                    'required' => true,
                    'visible' => true,
                    'options' => array(),
                    'placeholder' => '',
                    'default_value' => '',
                    'description' => '',
                ),
                array(
                    'id' => 'state',
                    'label' => 'الولاية',
                    'type' => 'select',
                    'required' => true,
                    'visible' => true,
                    'options' => array(),
                    'placeholder' => 'اختر الولاية',
                    'default_value' => '',
                    'description' => 'اختر الولاية الخاصة بك',
                ),
                array(
                    'id' => 'municipality',
                    'label' => 'البلدية',
                    'type' => 'select',  // تغيير من text إلى select
                    'required' => true,
                    'visible' => true,
                    'options' => array(),
                    'placeholder' => 'اختر البلدية',
                    'default_value' => '',
                    'description' => 'اختر البلدية الخاصة بك',
                ),
                array(
                    'id' => 'address',
                    'label' => 'العنوان التفصيلي',
                    'type' => 'text',
                    'required' => true,
                    'visible' => true,
                    'options' => array(),
                    'placeholder' => 'أدخل العنوان التفصيلي',
                    'default_value' => '',
                    'description' => 'العنوان التفصيلي لتوصيل الطلب',
                ),
            );

            // Default settings
            $default_settings = array(
                'button_text' => 'إتمام الطلب',
                'button_color' => '#3730a3',
                'button_gradient' => 'yes',
                'button_gradient_color' => '#312e81',
                'button_border_radius' => '4',
                'fields_border_radius' => '4',
                'fields_spacing' => 'medium',
                'card_shadow' => 'medium',
                'card_border_radius' => '8',
                'card_bg_color' => '#ffffff',
                'form_border_style' => 'dotted',
                'form_border_color' => '#3730a3',
                'form_border_width' => '2',
                'labels_font_weight' => 'bold',
                'icons_color' => '#3730a3',
                // إعدادات الشريط المثبت
                'sticky_bar_button_color' => '#3730a3',
                'sticky_bar_button_gradient' => 'yes',
                'sticky_bar_button_gradient_color' => '#312e81',
                // إعدادات زر الواتساب
                'enable_whatsapp_button' => 0,
                'whatsapp_number' => '',
                'whatsapp_button_text' => 'طلب عبر الواتساب',
                'whatsapp_button_color' => '#25D366',

            );

            // Insert default form
            $wpdb->insert(
                $forms_table,
                array(
                    'title' => '✨ أضف معلوماتك في الأسفل للطلب',
                    'description' => 'النموذج الافتراضي لصفحات المنتجات',
                    'fields' => serialize($default_fields),
                    'settings' => serialize($default_settings),
                    'status' => 'active',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql'),
                )
            );

            // Set default form ID option
            $default_form_id = $wpdb->insert_id;
            update_option('form_elrakami_default_form_id', $default_form_id);
        }

        // إصلاح النماذج الموجودة مسبقًا أيضًا
        self::fix_existing_forms();

        // تفعيل زر السلة لجميع المنتجات الموجودة
        self::enable_cart_for_all_products();

        // إنشاء جدول فئات المنتجات ورسوم التوصيل الإضافية
        self::create_product_categories_shipping_table();

        // Add plugin version to database
        update_option('form_elrakami_version', FORM_ELRAKAMI_VERSION);

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * إصلاح النماذج الموجودة مسبقًا
     * هذه الدالة تتحقق من وجود الحقول الإلزامية وتصحح نوع حقل البلدية
     *
     * @since    1.0.0
     */
    public static function fix_existing_forms() {
        global $wpdb;

        // Define table name
        $forms_table = $wpdb->prefix . 'form_elrakami_forms';

        // Get all forms
        $forms = $wpdb->get_results("SELECT id, fields FROM {$forms_table}", ARRAY_A);

        if (empty($forms)) {
            return;
        }

        // تعريف الحقول الإلزامية
        $required_fields = [
            'state' => [
                'id' => 'state',
                'label' => 'الولاية',
                'type' => 'select',
                'required' => true,
                'visible' => true,
                'placeholder' => 'اختر الولاية',
                'default_value' => '',
                'description' => 'اختر الولاية الخاصة بك',
                'options' => []
            ],
            'municipality' => [
                'id' => 'municipality',
                'label' => 'البلدية',
                'type' => 'select',  // يجب أن يكون من نوع select وليس text
                'required' => true,
                'visible' => true,
                'placeholder' => 'اختر البلدية',
                'default_value' => '',
                'description' => 'اختر البلدية الخاصة بك',
                'options' => []
            ],
            'address' => [
                'id' => 'address',
                'label' => 'العنوان التفصيلي',
                'type' => 'text',
                'required' => true,
                'visible' => true,
                'placeholder' => 'أدخل العنوان التفصيلي',
                'default_value' => '',
                'description' => 'العنوان التفصيلي لتوصيل الطلب',
                'options' => []
            ]
        ];

        foreach ($forms as $form) {
            $form_id = $form['id'];
            $fields = Form_Elrakami_Helper::unserialize_data($form['fields']);
            $updated = false;

            // فحص كل حقل إلزامي
            foreach ($required_fields as $field_id => $field_data) {
                $field_exists = false;
                $field_needs_correction = false;
                $field_index = -1;

                // البحث عن الحقل في القائمة الحالية
                foreach ($fields as $index => $field) {
                    if ($field['id'] === $field_id) {
                        $field_exists = true;
                        $field_index = $index;

                        // تحقق مما إذا كان نوع الحقل صحيحًا (خاصة للبلدية، يجب أن تكون select)
                        if ($field_id === 'municipality' && $field['type'] !== 'select') {
                            $field_needs_correction = true;
                        }

                        break;
                    }
                }

                // إذا كان الحقل موجودًا ولكن يحتاج إلى تصحيح
                if ($field_exists && $field_needs_correction) {
                    // تصحيح نوع الحقل فقط مع الحفاظ على جميع إعدادات المستخدم
                    $fields[$field_index]['type'] = 'select';
                    // التأكد من وجود مصفوفة الخيارات
                    if (!isset($fields[$field_index]['options'])) {
                        $fields[$field_index]['options'] = [];
                    }
                    $updated = true;
                }
                // إضافة الحقل إذا لم يكن موجودًا
                elseif (!$field_exists) {
                    $fields[] = $field_data;
                    $updated = true;
                }
            }

            // حفظ التغييرات إذا تم التعديل
            if ($updated) {
                $wpdb->update(
                    $forms_table,
                    array('fields' => Form_Elrakami_Helper::serialize_data($fields), 'updated_at' => current_time('mysql')),
                    array('id' => $form_id),
                    array('%s', '%s'),
                    array('%d')
                );
            }
        }
    }

    /**
     * إنشاء جدول فئات المنتجات ورسوم التوصيل الإضافية
     *
     * @since 1.0.0
     */
    public static function create_product_categories_shipping_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'form_elrakami_product_categories_shipping';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            category_id bigint(20) NOT NULL,
            category_name varchar(255) NOT NULL,
            category_type varchar(50) NOT NULL DEFAULT 'woocommerce',
            additional_shipping_cost decimal(10,2) NOT NULL DEFAULT 0.00,
            is_active tinyint(1) NOT NULL DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY category_unique (category_id, category_type),
            KEY category_type (category_type),
            KEY is_active (is_active)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * تفعيل زر السلة لجميع المنتجات الموجودة
     *
     * @since 1.0.0
     */
    public static function enable_cart_for_all_products() {
        // الحصول على جميع المنتجات
        $products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => '_form_elrakami_show_add_to_cart',
                    'compare' => 'NOT EXISTS'
                ),
                array(
                    'key' => '_form_elrakami_show_add_to_cart',
                    'value' => '',
                    'compare' => '='
                )
            )
        ));

        // تفعيل زر السلة للمنتجات التي لم يتم تعيين قيمة لها من قبل
        foreach ($products as $product) {
            update_post_meta($product->ID, '_form_elrakami_show_add_to_cart', 'yes');
        }
    }

}