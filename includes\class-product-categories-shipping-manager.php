<?php

/**
 * مدير فئات المنتجات ورسوم التوصيل الإضافية
 *
 * @package Form_Elrakami
 * @since 1.0.0
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * صنف إدارة فئات المنتجات ورسوم التوصيل الإضافية
 */
class Product_Categories_Shipping_Manager {

    /**
     * اسم الجدول
     */
    private $table_name;

    /**
     * تهيئة الصنف
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'form_elrakami_product_categories_shipping';
        
        // إضافة الإجراءات
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_save_category_shipping_cost', array($this, 'ajax_save_category_shipping_cost'));
        add_action('wp_ajax_delete_category_shipping_cost', array($this, 'ajax_delete_category_shipping_cost'));
        add_action('wp_ajax_get_category_shipping_costs', array($this, 'ajax_get_category_shipping_costs'));
    }

    /**
     * إضافة قائمة الإدارة
     */
    public function add_admin_menu() {
        add_submenu_page(
            'form-elrakami-settings',
            'فئات المنتجات ورسوم التوصيل',
            'فئات المنتجات',
            'manage_options',
            'form-elrakami-categories-shipping',
            array($this, 'render_admin_page')
        );
    }

    /**
     * عرض صفحة الإدارة
     */
    public function render_admin_page() {
        ?>
        <div class="wrap">
            <h1>إدارة فئات المنتجات ورسوم التوصيل الإضافية</h1>
            
            <div class="categories-shipping-container">
                <!-- قسم إضافة فئة جديدة -->
                <div class="add-category-section">
                    <h2>إضافة فئة جديدة</h2>
                    <form id="add-category-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="category_select">اختر الفئة</label>
                                </th>
                                <td>
                                    <select id="category_select" name="category_id" required>
                                        <option value="">-- اختر فئة --</option>
                                        <?php $this->render_category_options(); ?>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="additional_cost">الرسوم الإضافية (د.ج)</label>
                                </th>
                                <td>
                                    <input type="number" id="additional_cost" name="additional_cost" 
                                           min="0" step="50" value="0" required>
                                    <p class="description">الرسوم الإضافية التي ستُضاف لتكلفة التوصيل الأساسية</p>
                                </td>
                            </tr>
                        </table>
                        
                        <p class="submit">
                            <button type="submit" class="button button-primary">إضافة الفئة</button>
                        </p>
                    </form>
                </div>

                <!-- قسم عرض الفئات الموجودة -->
                <div class="existing-categories-section">
                    <h2>الفئات المُعرَّفة حالياً</h2>
                    <div id="categories-list">
                        <!-- سيتم تحميل القائمة عبر AJAX -->
                    </div>
                </div>
            </div>
        </div>

        <style>
        .categories-shipping-container {
            max-width: 1200px;
        }
        
        .add-category-section {
            background: #fff;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
        }
        
        .existing-categories-section {
            background: #fff;
            padding: 20px;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            margin-bottom: 10px;
            background: #f9f9f9;
        }
        
        .category-info {
            flex: 1;
        }
        
        .category-name {
            font-weight: bold;
            color: #2271b1;
        }
        
        .category-cost {
            color: #50575e;
            margin-top: 5px;
        }
        
        .category-actions {
            display: flex;
            gap: 10px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // تحميل قائمة الفئات عند تحميل الصفحة
            loadCategoriesList();
            
            // معالج إضافة فئة جديدة
            $('#add-category-form').on('submit', function(e) {
                e.preventDefault();
                
                var categoryId = $('#category_select').val();
                var additionalCost = $('#additional_cost').val();
                
                if (!categoryId) {
                    alert('يرجى اختيار فئة');
                    return;
                }
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'save_category_shipping_cost',
                        category_id: categoryId,
                        additional_cost: additionalCost,
                        nonce: '<?php echo wp_create_nonce('category_shipping_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('تم حفظ الفئة بنجاح');
                            $('#add-category-form')[0].reset();
                            loadCategoriesList();
                        } else {
                            alert('خطأ: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ في الاتصال');
                    }
                });
            });
            
            // معالج حذف فئة
            $(document).on('click', '.delete-category', function() {
                if (!confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
                    return;
                }
                
                var categoryId = $(this).data('id');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'delete_category_shipping_cost',
                        id: categoryId,
                        nonce: '<?php echo wp_create_nonce('category_shipping_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('تم حذف الفئة بنجاح');
                            loadCategoriesList();
                        } else {
                            alert('خطأ: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ في الاتصال');
                    }
                });
            });
            
            // دالة تحميل قائمة الفئات
            function loadCategoriesList() {
                $('#categories-list').html('<div class="loading">جاري التحميل...</div>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'get_category_shipping_costs',
                        nonce: '<?php echo wp_create_nonce('category_shipping_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#categories-list').html(response.data);
                        } else {
                            $('#categories-list').html('<p>لا توجد فئات مُعرَّفة حالياً</p>');
                        }
                    },
                    error: function() {
                        $('#categories-list').html('<p>حدث خطأ في تحميل البيانات</p>');
                    }
                });
            }
        });
        </script>
        <?php
    }

    /**
     * عرض خيارات الفئات
     */
    private function render_category_options() {
        // الحصول على فئات WooCommerce
        $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
        ));

        if (!is_wp_error($categories) && !empty($categories)) {
            foreach ($categories as $category) {
                echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
            }
        }
    }

    /**
     * AJAX: حفظ رسوم فئة
     */
    public function ajax_save_category_shipping_cost() {
        // التحقق من الأمان
        if (!wp_verify_nonce($_POST['nonce'], 'category_shipping_nonce')) {
            wp_send_json_error('فشل التحقق الأمني');
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('ليس لديك صلاحية للقيام بهذا الإجراء');
        }

        $category_id = intval($_POST['category_id']);
        $additional_cost = floatval($_POST['additional_cost']);

        if ($category_id <= 0) {
            wp_send_json_error('معرف الفئة غير صحيح');
        }

        // الحصول على اسم الفئة
        $category = get_term($category_id, 'product_cat');
        if (is_wp_error($category) || !$category) {
            wp_send_json_error('الفئة غير موجودة');
        }

        global $wpdb;

        // التحقق من وجود الفئة مسبقاً
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM {$this->table_name} WHERE category_id = %d AND category_type = 'woocommerce'",
            $category_id
        ));

        if ($existing) {
            // تحديث الفئة الموجودة
            $result = $wpdb->update(
                $this->table_name,
                array(
                    'additional_shipping_cost' => $additional_cost,
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $existing->id),
                array('%f', '%s'),
                array('%d')
            );
        } else {
            // إضافة فئة جديدة
            $result = $wpdb->insert(
                $this->table_name,
                array(
                    'category_id' => $category_id,
                    'category_name' => $category->name,
                    'category_type' => 'woocommerce',
                    'additional_shipping_cost' => $additional_cost,
                    'is_active' => 1
                ),
                array('%d', '%s', '%s', '%f', '%d')
            );
        }

        if ($result !== false) {
            wp_send_json_success('تم حفظ الفئة بنجاح');
        } else {
            wp_send_json_error('فشل في حفظ الفئة');
        }
    }

    /**
     * AJAX: حذف رسوم فئة
     */
    public function ajax_delete_category_shipping_cost() {
        // التحقق من الأمان
        if (!wp_verify_nonce($_POST['nonce'], 'category_shipping_nonce')) {
            wp_send_json_error('فشل التحقق الأمني');
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('ليس لديك صلاحية للقيام بهذا الإجراء');
        }

        $id = intval($_POST['id']);

        if ($id <= 0) {
            wp_send_json_error('معرف غير صحيح');
        }

        global $wpdb;

        $result = $wpdb->delete(
            $this->table_name,
            array('id' => $id),
            array('%d')
        );

        if ($result !== false) {
            wp_send_json_success('تم حذف الفئة بنجاح');
        } else {
            wp_send_json_error('فشل في حذف الفئة');
        }
    }

    /**
     * AJAX: الحصول على قائمة رسوم الفئات
     */
    public function ajax_get_category_shipping_costs() {
        // التحقق من الأمان
        if (!wp_verify_nonce($_POST['nonce'], 'category_shipping_nonce')) {
            wp_send_json_error('فشل التحقق الأمني');
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('ليس لديك صلاحية للقيام بهذا الإجراء');
        }

        global $wpdb;

        $categories = $wpdb->get_results(
            "SELECT * FROM {$this->table_name} WHERE is_active = 1 ORDER BY category_name ASC"
        );

        if (empty($categories)) {
            wp_send_json_success('<p>لا توجد فئات مُعرَّفة حالياً</p>');
        }

        $html = '';
        foreach ($categories as $category) {
            $html .= '<div class="category-item">';
            $html .= '<div class="category-info">';
            $html .= '<div class="category-name">' . esc_html($category->category_name) . '</div>';
            $html .= '<div class="category-cost">الرسوم الإضافية: ' . number_format($category->additional_shipping_cost, 0) . ' د.ج</div>';
            $html .= '</div>';
            $html .= '<div class="category-actions">';
            $html .= '<button type="button" class="button delete-category" data-id="' . esc_attr($category->id) . '">حذف</button>';
            $html .= '</div>';
            $html .= '</div>';
        }

        wp_send_json_success($html);
    }

    /**
     * الحصول على الرسوم الإضافية لفئة معينة
     */
    public function get_category_additional_cost($category_id, $category_type = 'woocommerce') {
        global $wpdb;

        $cost = $wpdb->get_var($wpdb->prepare(
            "SELECT additional_shipping_cost FROM {$this->table_name}
             WHERE category_id = %d AND category_type = %s AND is_active = 1",
            $category_id,
            $category_type
        ));

        return $cost ? floatval($cost) : 0;
    }

    /**
     * حساب إجمالي الرسوم الإضافية للمنتجات في السلة
     */
    public function calculate_cart_additional_shipping_costs($cart_items) {
        $total_additional_cost = 0;
        $processed_categories = array(); // لتجنب تكرار الرسوم لنفس الفئة

        foreach ($cart_items as $item) {
            $product_id = $item['product_id'];
            $variation_id = isset($item['variation_id']) ? $item['variation_id'] : 0;

            // استخدام معرف المتغير إذا كان موجوداً، وإلا استخدام معرف المنتج الأساسي
            $product_to_check = $variation_id > 0 ? $variation_id : $product_id;

            // الحصول على فئات المنتج
            $product_categories = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'ids'));

            if (!empty($product_categories)) {
                foreach ($product_categories as $category_id) {
                    // تجنب تكرار الرسوم لنفس الفئة
                    if (!in_array($category_id, $processed_categories)) {
                        $additional_cost = $this->get_category_additional_cost($category_id);
                        if ($additional_cost > 0) {
                            $total_additional_cost += $additional_cost;
                            $processed_categories[] = $category_id;
                        }
                    }
                }
            }
        }

        return $total_additional_cost;
    }

    /**
     * الحصول على جميع الفئات المُعرَّفة
     */
    public function get_all_categories() {
        global $wpdb;

        return $wpdb->get_results(
            "SELECT * FROM {$this->table_name} WHERE is_active = 1 ORDER BY category_name ASC"
        );
    }
}

// تهيئة الصنف
new Product_Categories_Shipping_Manager();
